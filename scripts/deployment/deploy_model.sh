#!/bin/bash

# Stop and remove any existing container
echo "Stopping any existing container..."
docker stop my_vllm_container 2>/dev/null
docker rm my_vllm_container 2>/dev/null

# Deploy with docker
echo "Deploying the model with Docker..."
docker run \
    --name my_vllm_container \
    -v ~/.cache/huggingface:/root/.cache/huggingface \
    --env "HUGGING_FACE_HUB_TOKEN=your_huggingface_token_here" \
    -p 8000:8000 \
    --ipc=host \
    -d \
    ghcr.io/huggingface/text-generation-inference:latest \
    --model-id jianghc/medical_chatbot

echo "Model deployment initiated. It may take some time to download and load the model."
echo "You can check the status with: docker logs -f my_vllm_container"
